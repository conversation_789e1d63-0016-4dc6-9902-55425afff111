import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Users, Bell, Upload } from 'lucide-react';
import { HiMiniUserCircle } from "react-icons/hi2";
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import Team from './tabs/team';
import Notification from './tabs/notification';

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('account');
  const [formData, setFormData] = useState({
    firstName: 'Arlene',
    lastName: 'McCoy',
    email: '<EMAIL>',
    phone: '***********'
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handlePhoneChange = (value: string) => {
    setFormData({
      ...formData,
      phone: value
    });
  };

  return (
    <div className="flex h-full">
      {/* Left sidebar */}
      <div className="w-64 border-r border-gray-200 min-h-screen">
        <h1 className="text-xl font-bold p-6">Settings</h1>
        <div className="flex flex-col px-4 space-y-2">
          <button
            className={`flex items-center gap-2 p-4 text-left rounded-md ${
              activeTab === 'account' ? 'bg-orange-100 text-orange-500' : 'hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('account')}
          >
            <HiMiniUserCircle size={18} />
            <span>Your account</span>
          </button>
          <button
            className={`flex items-center gap-2 p-4 text-left rounded-md ${
              activeTab === 'team' ? 'bg-orange-100 text-orange-500' : 'hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('team')}
          >
            <Users size={18} />
            <span>Team</span>
          </button>
          <button
            className={`flex items-center gap-2 p-4 text-left rounded-md ${
              activeTab === 'notifications' ? 'bg-orange-100 text-orange-500' : 'hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('notifications')}
          >
            <Bell size={18} />
            <span>Notifications</span>
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 p-6">
        {activeTab === 'account' && (
          <div className="flex flex-col items-center">
            {/* <h2 className="text-2xl font-bold mb-8">Your account</h2> */}
            
            <div className="max-w-2xl w-full">
              <h3 className="text-xl font-medium mb-6 mt-8">Personal details</h3>
              
              <div className="mb-6">
                <p className="mb-2">Profile image</p>
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center text-gray-500 cursor-pointer hover:bg-gray-200 transition-colors">
                  <Upload size={20} className="text-orange-300" />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block mb-2">First name</label>
                  <Input 
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    className="h-16 py-4"
                  />
                </div>
                <div>
                  <label className="block mb-2">Last name</label>
                  <Input 
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    className="h-16 py-4"
                  />
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block mb-2">Email</label>
                <Input 
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="h-16 py-4"
                />
              </div>
              
              <div className="mb-8">
                <label className="block mb-2">Phone number</label>
                <PhoneInput
                  country={'gh'}
                  value={formData.phone}
                  onChange={handlePhoneChange}
                  enableSearch={true}
                  searchPlaceholder="Search country..."
                  inputStyle={{
                    width: '100%',
                    height: '48px',
                    fontSize: '16px',
                    paddingLeft: '48px'
                  }}
                  containerStyle={{
                    width: '100%'
                  }}
                  dropdownStyle={{
                    width: '300px'
                  }}
                />
              </div>
              
              <div className="flex justify-end gap-3">
                <Button variant="outline" className="h-12 px-6">Cancel</Button>
                <Button className="bg-orange-500 hover:bg-orange-600 h-12 px-6">Save changes</Button>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'team' && (
          <Team />
        )}
        
        {activeTab === 'notifications' && (
          <Notification />
        )}
      </div>
    </div>
  );
};

export default Settings;





