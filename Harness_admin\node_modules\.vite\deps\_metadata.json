{"hash": "66cc0399", "configHash": "9737c487", "lockfileHash": "7371cd78", "browserHash": "84ca4fd5", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "06681c0f", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "50c1a1f6", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "96b488e1", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "a3fd562b", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "7911277a", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "03a1c48c", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "22dc52e7", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "672f99fd", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "0fb3d7d0", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "aa76c3f8", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "fd7c5e23", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "785afe18", "needsInterop": false}, "@tanstack/react-table": {"src": "../../@tanstack/react-table/build/lib/index.mjs", "file": "@tanstack_react-table.js", "fileHash": "296a67eb", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "dda47131", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "d9ff0501", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "cfca7897", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0ef9eba9", "needsInterop": true}, "react-icons/hi2": {"src": "../../../../node_modules/react-icons/hi2/index.mjs", "file": "react-icons_hi2.js", "fileHash": "5840bcc0", "needsInterop": false}, "react-phone-input-2": {"src": "../../react-phone-input-2/lib/lib.js", "file": "react-phone-input-2.js", "fileHash": "c0c36521", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "832edc13", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "2419ba68", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "f922d0cb", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "44d301f3", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-DGZ242WS": {"file": "chunk-DGZ242WS.js"}, "chunk-6ZMM2PAV": {"file": "chunk-6ZMM2PAV.js"}, "chunk-FXVEBMXU": {"file": "chunk-FXVEBMXU.js"}, "chunk-WXIJ4NI6": {"file": "chunk-WXIJ4NI6.js"}, "chunk-ERAQVAZD": {"file": "chunk-ERAQVAZD.js"}, "chunk-77OMBBSX": {"file": "chunk-77OMBBSX.js"}, "chunk-IAYGKC26": {"file": "chunk-IAYGKC26.js"}, "chunk-UKL35QUL": {"file": "chunk-UKL35QUL.js"}, "chunk-BKECY2N6": {"file": "chunk-BKECY2N6.js"}, "chunk-SHOYMLYQ": {"file": "chunk-SHOYMLYQ.js"}, "chunk-SFVKNZMI": {"file": "chunk-SFVKNZMI.js"}, "chunk-5RANCIOP": {"file": "chunk-5RANCIOP.js"}, "chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-JNNNAK6O": {"file": "chunk-JNNNAK6O.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}