{"name": "harness", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.80.7", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}