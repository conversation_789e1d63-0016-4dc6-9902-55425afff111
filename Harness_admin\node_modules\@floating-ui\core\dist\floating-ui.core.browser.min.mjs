const t=["top","right","bottom","left"],e=["start","end"],n=t.reduce(((t,n)=>t.concat(n,n+"-"+e[0],n+"-"+e[1])),[]),i=Math.min,r=Math.max,o={left:"right",right:"left",bottom:"top",top:"bottom"},a={start:"end",end:"start"};function l(t,e,n){return r(t,i(e,n))}function s(t,e){return"function"==typeof t?t(e):t}function c(t){return t.split("-")[0]}function f(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function u(t){return"y"===t?"height":"width"}function d(t){return["top","bottom"].includes(c(t))?"y":"x"}function g(t){return m(d(t))}function p(t,e,n){void 0===n&&(n=!1);const i=f(t),r=g(t),o=u(r);let a="x"===r?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[o]>e.floating[o]&&(a=y(a)),[a,y(a)]}function h(t){return t.replace(/start|end/g,(t=>a[t]))}function y(t){return t.replace(/left|right|bottom|top/g,(t=>o[t]))}function w(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function x(t){const{x:e,y:n,width:i,height:r}=t;return{width:i,height:r,top:n,left:e,right:e+i,bottom:n+r,x:e,y:n}}function v(t,e,n){let{reference:i,floating:r}=t;const o=d(e),a=g(e),l=u(a),s=c(e),m="y"===o,p=i.x+i.width/2-r.width/2,h=i.y+i.height/2-r.height/2,y=i[l]/2-r[l]/2;let w;switch(s){case"top":w={x:p,y:i.y-r.height};break;case"bottom":w={x:p,y:i.y+i.height};break;case"right":w={x:i.x+i.width,y:h};break;case"left":w={x:i.x-r.width,y:h};break;default:w={x:i.x,y:i.y}}switch(f(e)){case"start":w[a]-=y*(n&&m?-1:1);break;case"end":w[a]+=y*(n&&m?-1:1)}return w}const b=async(t,e,n)=>{const{placement:i="bottom",strategy:r="absolute",middleware:o=[],platform:a}=n,l=o.filter(Boolean),s=await(null==a.isRTL?void 0:a.isRTL(e));let c=await a.getElementRects({reference:t,floating:e,strategy:r}),{x:f,y:m}=v(c,i,s),u=i,d={},g=0;for(let n=0;n<l.length;n++){const{name:o,fn:p}=l[n],{x:h,y:y,data:w,reset:x}=await p({x:f,y:m,initialPlacement:i,placement:u,strategy:r,middlewareData:d,rects:c,platform:a,elements:{reference:t,floating:e}});f=null!=h?h:f,m=null!=y?y:m,d={...d,[o]:{...d[o],...w}},x&&g<=50&&(g++,"object"==typeof x&&(x.placement&&(u=x.placement),x.rects&&(c=!0===x.rects?await a.getElementRects({reference:t,floating:e,strategy:r}):x.rects),({x:f,y:m}=v(c,u,s))),n=-1)}return{x:f,y:m,placement:u,strategy:r,middlewareData:d}};async function A(t,e){var n;void 0===e&&(e={});const{x:i,y:r,platform:o,rects:a,elements:l,strategy:c}=t,{boundary:f="clippingAncestors",rootBoundary:m="viewport",elementContext:u="floating",altBoundary:d=!1,padding:g=0}=s(e,t),p=w(g),h=l[d?"floating"===u?"reference":"floating":u],y=x(await o.getClippingRect({element:null==(n=await(null==o.isElement?void 0:o.isElement(h)))||n?h:h.contextElement||await(null==o.getDocumentElement?void 0:o.getDocumentElement(l.floating)),boundary:f,rootBoundary:m,strategy:c})),v="floating"===u?{x:i,y:r,width:a.floating.width,height:a.floating.height}:a.reference,b=await(null==o.getOffsetParent?void 0:o.getOffsetParent(l.floating)),A=await(null==o.isElement?void 0:o.isElement(b))&&await(null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},R=x(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:v,offsetParent:b,strategy:c}):v);return{top:(y.top-R.top+p.top)/A.y,bottom:(R.bottom-y.bottom+p.bottom)/A.y,left:(y.left-R.left+p.left)/A.x,right:(R.right-y.right+p.right)/A.x}}const R=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:r,placement:o,rects:a,platform:c,elements:m,middlewareData:d}=e,{element:p,padding:h=0}=s(t,e)||{};if(null==p)return{};const y=w(h),x={x:n,y:r},v=g(o),b=u(v),A=await c.getDimensions(p),R="y"===v,D=R?"top":"left",P=R?"bottom":"right",E=R?"clientHeight":"clientWidth",O=a.reference[b]+a.reference[v]-x[v]-a.floating[b],T=x[v]-a.reference[v],L=await(null==c.getOffsetParent?void 0:c.getOffsetParent(p));let k=L?L[E]:0;k&&await(null==c.isElement?void 0:c.isElement(L))||(k=m.floating[E]||a.floating[b]);const B=O/2-T/2,C=k/2-A[b]/2-1,H=i(y[D],C),S=i(y[P],C),F=H,M=k-A[b]-S,V=k/2-A[b]/2+B,W=l(F,V,M),j=!d.arrow&&null!=f(o)&&V!==W&&a.reference[b]/2-(V<F?H:S)-A[b]/2<0,z=j?V<F?V-F:V-M:0;return{[v]:x[v]+z,data:{[v]:W,centerOffset:V-W-z,...j&&{alignmentOffset:z}},reset:j}}});const D=function(t){return void 0===t&&(t={}),{name:"autoPlacement",options:t,async fn(e){var i,r,o;const{rects:a,middlewareData:l,placement:m,platform:u,elements:d}=e,{crossAxis:g=!1,alignment:y,allowedPlacements:w=n,autoAlignment:x=!0,...v}=s(t,e),b=void 0!==y||w===n?function(t,e,n){return(t?[...n.filter((e=>f(e)===t)),...n.filter((e=>f(e)!==t))]:n.filter((t=>c(t)===t))).filter((n=>!t||f(n)===t||!!e&&h(n)!==n))}(y||null,x,w):w,R=await A(e,v),D=(null==(i=l.autoPlacement)?void 0:i.index)||0,P=b[D];if(null==P)return{};const E=p(P,a,await(null==u.isRTL?void 0:u.isRTL(d.floating)));if(m!==P)return{reset:{placement:b[0]}};const O=[R[c(P)],R[E[0]],R[E[1]]],T=[...(null==(r=l.autoPlacement)?void 0:r.overflows)||[],{placement:P,overflows:O}],L=b[D+1];if(L)return{data:{index:D+1,overflows:T},reset:{placement:L}};const k=T.map((t=>{const e=f(t.placement);return[t.placement,e&&g?t.overflows.slice(0,2).reduce(((t,e)=>t+e),0):t.overflows[0],t.overflows]})).sort(((t,e)=>t[1]-e[1])),B=(null==(o=k.filter((t=>t[2].slice(0,f(t[0])?2:3).every((t=>t<=0))))[0])?void 0:o[0])||k[0][0];return B!==m?{data:{index:D+1,overflows:T},reset:{placement:B}}:{}}}},P=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i;const{placement:r,middlewareData:o,rects:a,initialPlacement:l,platform:m,elements:u}=e,{mainAxis:g=!0,crossAxis:w=!0,fallbackPlacements:x,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:R=!0,...D}=s(t,e);if(null!=(n=o.arrow)&&n.alignmentOffset)return{};const P=c(r),E=d(l),O=c(l)===l,T=await(null==m.isRTL?void 0:m.isRTL(u.floating)),L=x||(O||!R?[y(l)]:function(t){const e=y(t);return[h(t),e,h(e)]}(l)),k="none"!==b;!x&&k&&L.push(...function(t,e,n,i){const r=f(t);let o=function(t,e,n){const i=["left","right"],r=["right","left"],o=["top","bottom"],a=["bottom","top"];switch(t){case"top":case"bottom":return n?e?r:i:e?i:r;case"left":case"right":return e?o:a;default:return[]}}(c(t),"start"===n,i);return r&&(o=o.map((t=>t+"-"+r)),e&&(o=o.concat(o.map(h)))),o}(l,R,b,T));const B=[l,...L],C=await A(e,D),H=[];let S=(null==(i=o.flip)?void 0:i.overflows)||[];if(g&&H.push(C[P]),w){const t=p(r,a,T);H.push(C[t[0]],C[t[1]])}if(S=[...S,{placement:r,overflows:H}],!H.every((t=>t<=0))){var F,M;const t=((null==(F=o.flip)?void 0:F.index)||0)+1,e=B[t];if(e){if(!("alignment"===w&&E!==d(e))||S.every((t=>t.overflows[0]>0&&d(t.placement)===E)))return{data:{index:t,overflows:S},reset:{placement:e}}}let n=null==(M=S.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:M.placement;if(!n)switch(v){case"bestFit":{var V;const t=null==(V=S.filter((t=>{if(k){const e=d(t.placement);return e===E||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:V[0];t&&(n=t);break}case"initialPlacement":n=l}if(r!==n)return{reset:{placement:n}}}return{}}}};function E(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function O(e){return t.some((t=>e[t]>=0))}const T=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:i="referenceHidden",...r}=s(t,e);switch(i){case"referenceHidden":{const t=E(await A(e,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:O(t)}}}case"escaped":{const t=E(await A(e,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:O(t)}}}default:return{}}}}};function L(t){const e=i(...t.map((t=>t.left))),n=i(...t.map((t=>t.top)));return{x:e,y:n,width:r(...t.map((t=>t.right)))-e,height:r(...t.map((t=>t.bottom)))-n}}const k=function(t){return void 0===t&&(t={}),{name:"inline",options:t,async fn(e){const{placement:n,elements:o,rects:a,platform:l,strategy:f}=e,{padding:m=2,x:u,y:g}=s(t,e),p=Array.from(await(null==l.getClientRects?void 0:l.getClientRects(o.reference))||[]),h=function(t){const e=t.slice().sort(((t,e)=>t.y-e.y)),n=[];let i=null;for(let t=0;t<e.length;t++){const r=e[t];!i||r.y-i.y>i.height/2?n.push([r]):n[n.length-1].push(r),i=r}return n.map((t=>x(L(t))))}(p),y=x(L(p)),v=w(m);const b=await l.getElementRects({reference:{getBoundingClientRect:function(){if(2===h.length&&h[0].left>h[1].right&&null!=u&&null!=g)return h.find((t=>u>t.left-v.left&&u<t.right+v.right&&g>t.top-v.top&&g<t.bottom+v.bottom))||y;if(h.length>=2){if("y"===d(n)){const t=h[0],e=h[h.length-1],i="top"===c(n),r=t.top,o=e.bottom,a=i?t.left:e.left,l=i?t.right:e.right;return{top:r,bottom:o,left:a,right:l,width:l-a,height:o-r,x:a,y:r}}const t="left"===c(n),e=r(...h.map((t=>t.right))),o=i(...h.map((t=>t.left))),a=h.filter((n=>t?n.left===o:n.right===e)),l=a[0].top,s=a[a.length-1].bottom;return{top:l,bottom:s,left:o,right:e,width:e-o,height:s-l,x:o,y:l}}return y}},floating:o.floating,strategy:f});return a.reference.x!==b.reference.x||a.reference.y!==b.reference.y||a.reference.width!==b.reference.width||a.reference.height!==b.reference.height?{reset:{rects:b}}:{}}}};const B=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;const{x:r,y:o,placement:a,middlewareData:l}=e,m=await async function(t,e){const{placement:n,platform:i,elements:r}=t,o=await(null==i.isRTL?void 0:i.isRTL(r.floating)),a=c(n),l=f(n),m="y"===d(n),u=["left","top"].includes(a)?-1:1,g=o&&m?-1:1,p=s(e,t);let{mainAxis:h,crossAxis:y,alignmentAxis:w}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return l&&"number"==typeof w&&(y="end"===l?-1*w:w),m?{x:y*g,y:h*u}:{x:h*u,y:y*g}}(e,t);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(i=l.arrow)&&i.alignmentOffset?{}:{x:r+m.x,y:o+m.y,data:{...m,placement:a}}}}},C=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:i,placement:r}=e,{mainAxis:o=!0,crossAxis:a=!1,limiter:f={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...u}=s(t,e),g={x:n,y:i},p=await A(e,u),h=d(c(r)),y=m(h);let w=g[y],x=g[h];if(o){const t="y"===y?"bottom":"right";w=l(w+p["y"===y?"top":"left"],w,w-p[t])}if(a){const t="y"===h?"bottom":"right";x=l(x+p["y"===h?"top":"left"],x,x-p[t])}const v=f.fn({...e,[y]:w,[h]:x});return{...v,data:{x:v.x-n,y:v.y-i,enabled:{[y]:o,[h]:a}}}}}},H=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:i,placement:r,rects:o,middlewareData:a}=e,{offset:l=0,mainAxis:f=!0,crossAxis:u=!0}=s(t,e),g={x:n,y:i},p=d(r),h=m(p);let y=g[h],w=g[p];const x=s(l,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){const t="y"===h?"height":"width",e=o.reference[h]-o.floating[t]+v.mainAxis,n=o.reference[h]+o.reference[t]-v.mainAxis;y<e?y=e:y>n&&(y=n)}if(u){var b,A;const t="y"===h?"width":"height",e=["top","left"].includes(c(r)),n=o.reference[p]-o.floating[t]+(e&&(null==(b=a.offset)?void 0:b[p])||0)+(e?0:v.crossAxis),i=o.reference[p]+o.reference[t]+(e?0:(null==(A=a.offset)?void 0:A[p])||0)-(e?v.crossAxis:0);w<n?w=n:w>i&&(w=i)}return{[h]:y,[p]:w}}}},S=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,o;const{placement:a,rects:l,platform:m,elements:u}=e,{apply:g=()=>{},...p}=s(t,e),h=await A(e,p),y=c(a),w=f(a),x="y"===d(a),{width:v,height:b}=l.floating;let R,D;"top"===y||"bottom"===y?(R=y,D=w===(await(null==m.isRTL?void 0:m.isRTL(u.floating))?"start":"end")?"left":"right"):(D=y,R="end"===w?"top":"bottom");const P=b-h.top-h.bottom,E=v-h.left-h.right,O=i(b-h[R],P),T=i(v-h[D],E),L=!e.middlewareData.shift;let k=O,B=T;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(B=E),null!=(o=e.middlewareData.shift)&&o.enabled.y&&(k=P),L&&!w){const t=r(h.left,0),e=r(h.right,0),n=r(h.top,0),i=r(h.bottom,0);x?B=v-2*(0!==t||0!==e?t+e:r(h.left,h.right)):k=b-2*(0!==n||0!==i?n+i:r(h.top,h.bottom))}await g({...e,availableWidth:B,availableHeight:k});const C=await m.getDimensions(u.floating);return v!==C.width||b!==C.height?{reset:{rects:!0}}:{}}}};export{R as arrow,D as autoPlacement,b as computePosition,A as detectOverflow,P as flip,T as hide,k as inline,H as limitShift,B as offset,x as rectToClientRect,C as shift,S as size};
