import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ChevronDown, Plus, X } from 'lucide-react';

interface TeamMemberInput {
  email: string;
  accessLevel: string;
}

interface AddTeamPageProps {
  onCancel: () => void;
  onAddTeammate: (members: TeamMemberInput[]) => void;
}

export default function AddTeamPage({ onCancel, onAddTeammate }: AddTeamPageProps) {
  const [teamMembers, setTeamMembers] = useState<TeamMemberInput[]>([
    { email: '', accessLevel: '' }
  ]);

  const handleEmailChange = (index: number, value: string) => {
    const updatedMembers = [...teamMembers];
    updatedMembers[index].email = value;
    setTeamMembers(updatedMembers);
  };

  const handleAccessLevelChange = (index: number, value: string) => {
    const updatedMembers = [...teamMembers];
    updatedMembers[index].accessLevel = value;
    setTeamMembers(updatedMembers);
  };

  const addAnotherMember = () => {
    setTeamMembers([...teamMembers, { email: '', accessLevel: '' }]);
  };

  const removeMember = (index: number) => {
    const updatedMembers = [...teamMembers];
    updatedMembers.splice(index, 1);
    setTeamMembers(updatedMembers);
  };

  const handleSubmit = () => {
    // Filter out empty entries
    const validMembers = teamMembers.filter(member => member.email.trim() !== '');
    onAddTeammate(validMembers);
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      {/* <h1 className="text-xl font-bold mb-8">Add teammate</h1> */}
      
      <div className="mb-6 mt-10 ">
        <h2 className="text-3xl font-medium mb-4">Team</h2>
        
        {teamMembers.map((member, index) => (
          <div key={index} className="mb-4 relative">
            <div className="grid grid-cols-2 gap-4 border p-6 rounded-xl">
              <div>
                <label className="block text-sm mb-1">Email</label>
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={member.email}
                  onChange={(e) => handleEmailChange(index, e.target.value)}
                  className="w-full h-12 py-3"
                />
              </div>
              <div>
                <label className="block text-sm mb-1">Access level</label>
                <div className="relative">
                  <select
                    value={member.accessLevel}
                    onChange={(e) => handleAccessLevelChange(index, e.target.value)}
                    className="w-full h-12 px-3 py-3 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="" disabled>Select</option>
                    <option value="Admin">Admin</option>
                    <option value="Operations">Operations</option>
                    <option value="Viewer">Viewer</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  </div>
                </div>
              </div>
            </div>
            
            {/* Show delete button only for additional members (index > 0) */}
            {index > 0 && (
              <button 
                onClick={() => removeMember(index)}
                className="absolute -right-8 top-8 text-gray-400 hover:text-gray-600"
                aria-label="Remove team member"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        ))}
        
        <button 
          onClick={addAnotherMember}
          className="text-orange-500 flex items-center gap-1 text-sm font-medium mt-2 cursor-pointer"
        >
          <Plus className="h-4 w-4" />
          Add another
        </button>
      </div>
      
      <div className="flex justify-end gap-3 mt-8">
        <Button 
          variant="outline" 
          onClick={onCancel}
          className="px-4 py-4"
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-4"
        >
          Add teammate
        </Button>
      </div>
    </div>
  );
}
