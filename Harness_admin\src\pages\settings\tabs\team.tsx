import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, MoreVertical, Filter } from 'lucide-react';
import AddTeamMatePage from '../addTeamMatePage';

interface TeamMember {
  id: number;
  name: string;
  email: string;
  accessLevel: 'Admin' | 'Operations' | 'Viewer';
}

interface TeamMemberInput {
  email: string;
  accessLevel: string;
}

export default function Team() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddTeammate, setShowAddTeammate] = useState(false);
  
  // Mock data for team members
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([
    { id: 1, name: '<PERSON>', email: '<EMAIL>', accessLevel: 'Admin' },
    { id: 2, name: '<PERSON>', email: 'j<PERSON><PERSON><PERSON><PERSON>@example.com', accessLevel: 'Admin' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', accessLevel: 'Operations' },
  ]);

  const filteredMembers = teamMembers.filter(member => 
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddTeammate = (newMembers: TeamMemberInput[]) => {
    // todo:API call here
    const addedMembers = newMembers.map((member, index) => ({
      id: teamMembers.length + index + 1,
      name: member.email.split('@')[0], // Just for demo, extracting name from email
      email: member.email,
      accessLevel: member.accessLevel as 'Admin' | 'Operations' | 'Viewer',
    }));
    
    setTeamMembers([...teamMembers, ...addedMembers]);
    setShowAddTeammate(false);
  };

  if (showAddTeammate) {
    return (
      <AddTeamMatePage 
        onCancel={() => setShowAddTeammate(false)}
        onAddTeammate={handleAddTeammate}
      />
    );
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="h-10 flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Search"
              className="pl-10 h-10 w-[300px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        
        <Button 
          className="bg-orange-500 hover:bg-orange-600 h-10"
          onClick={() => setShowAddTeammate(true)}
        >
          Add teammate
        </Button>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200">
        <table className="w-full">
          <thead className="bg-gray-50 text-left">
            <tr>
              <th className="px-6 py-4 text-sm font-medium text-gray-500">Name</th>
              <th className="px-6 py-4 text-sm font-medium text-gray-500">Email</th>
              <th className="px-6 py-4 text-sm font-medium text-gray-500">Access level</th>
              <th className="px-6 py-4 text-sm font-medium text-gray-500 w-10"></th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {filteredMembers.map((member) => (
              <tr key={member.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{member.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">{member.email}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    member.accessLevel === 'Admin' 
                      ? 'bg-blue-100 text-blue-800' 
                      : member.accessLevel === 'Operations'
                      ? 'bg-orange-100 text-orange-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {member.accessLevel}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right">
                  <button className="text-gray-400 hover:text-gray-600">
                    <MoreVertical className="h-5 w-5" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
