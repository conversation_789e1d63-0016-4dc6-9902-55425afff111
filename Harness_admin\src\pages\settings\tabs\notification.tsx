import { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
}

export default function Notification() {
  const [notificationSettings, setNotificationSettings] = useState<NotificationSetting[]>([
    {
      id: 'project-opportunities',
      title: 'Project Opportunities & Recommendations',
      description: 'New project matches based on criteria, AI-curated recommendations and updates on saved or followed projects',
      enabled: false
    },
    {
      id: 'project-discussions',
      title: 'Project Discussions & Offers',
      description: 'New messages from developers, offer responses (accepted, declined, countered) and contract signing status',
      enabled: false
    },
    {
      id: 'portfolio-progress',
      title: 'Portfolio & Progress',
      description: 'Milestone completed by developer, disbursement requested, project performance update',
      enabled: true
    },
    {
      id: 'project-activity',
      title: 'Project Activity',
      description: 'Offer received from investor, offer accepted/declined, investor requests edits to contract',
      enabled: false
    },
    {
      id: 'funding-progress',
      title: 'Funding & Progress',
      description: 'Funds disbursed, investor marks phase/milestone as reviewed, contract status changes',
      enabled: false
    }
  ]);

  const [emailFrequency, setEmailFrequency] = useState('never');

  const handleToggleNotification = (id: string) => {
    setNotificationSettings(prevSettings => 
      prevSettings.map(setting => 
        setting.id === id ? { ...setting, enabled: !setting.enabled } : setting
      )
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto mt-16">
      {/* <h1 className="text-2xl font-bold mb-8">Notifications</h1> */}
      
      <div className="space-y-6">
        {notificationSettings.map(setting => (
          <div 
            key={setting.id}
            className="bg-white rounded-lg p-6 border border-gray-200 flex justify-between items-center"
          >
            <div className="space-y-1 max-w-3xl">
              <h3 className="text-lg font-medium">{setting.title}</h3>
              <p className="text-gray-500 text-sm">{setting.description}</p>
            </div>
            <Switch 
              checked={setting.enabled}
              onCheckedChange={() => handleToggleNotification(setting.id)}
              className={`${setting.enabled ? 'bg-orange-500' : 'bg-gray-200'}`}
            />
          </div>
        ))}
      </div>

      <div className="mt-10 space-y-2 border-t py-4">
        <h3 className="text-lg font-medium">Send me email notifications:</h3>
        <p className="text-gray-500 text-sm">Select how often should I receive email notifications</p>

        <div className="w-64 mt-2 mx-auto">
          <Select value={emailFrequency} onValueChange={setEmailFrequency}>
            <SelectTrigger className="w-full h-12 border border-gray-300">
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <div></div>
            <SelectContent>
              <SelectItem value="never">Never</SelectItem>
              <SelectItem value="immediately">Immediately</SelectItem>
              <SelectItem value="daily">Daily digest</SelectItem>
              <SelectItem value="weekly">Weekly digest</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
